@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: "bagisto-shop";
    src: url("../fonts/bagisto-shop.woff") format("woff");
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

img.lazy {
  visibility: hidden;
}

@layer components {
    ::selection {
        background-color: rgba(6, 12, 59, .2);
    }

    input {
        @apply outline-none;
    }

    button:disabled {
        @apply cursor-not-allowed opacity-50;
    }

    button:disabled:hover {
        @apply cursor-not-allowed opacity-50;
    }

    [class^="icon-"],
    [class*=" icon-"] {
        /* use !important to prevent issues with browser extensions that change fonts */
        font-family: "bagisto-shop" !important;
        speak: never;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1 !important;
        color: #757c5b;

        /* Better Font Rendering =========== */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    html.dark [class^="icon-"],
    html.dark [class*=" icon-"]{
        color: #d1d5db;
    }

    .box-shadow {
        @apply dark:border-gray-800 shadow-[0px_0px_0px_0px_rgba(0,0,0,0.03),0px_1px_1px_0px_rgba(0,0,0,0.03),0px_3px_3px_0px_rgba(0,0,0,0.03),0px_6px_4px_0px_rgba(0,0,0,0.02),0px_11px_4px_0px_rgba(0,0,0,0.00),0px_17px_5px_0px_rgba(0,0,0,0.00)];
    }

    .table-responsive.widian-table {
        box-shadow: 0 0 5px #efece2;
    }

    .icon-checkout-address:before {
        content: "\e944";
    }

    .icon-edit:before {
        content: "\e943";
    }

    .icon-add-new:before {
        content: "\e900";
    }

    .icon-arrow-down:before {
        content: "\e901";
    }

    .icon-arrow-left-stylish:before {
        content: "\e902";
    }

    .icon-arrow-left:before {
        content: "\e903";
    }

    .icon-arrow-right-stylish:before {
        content: "\e904";
    }

    .icon-arrow-right:before {
        content: "\e905";
    }

    .icon-arrow-up:before {
        content: "\e906";
    }

    .icon-astreisk:before {
        content: "\e907";
    }

    .icon-bin:before {
        content: "\e908";
    }

    .icon-box-fill:before {
        content: "\e909";
    }

    .icon-calendar:before {
        content: "\e90a";
    }

    .icon-camera-fill:before {
        content: "\e90b";
    }

    .icon-camera:before {
        content: "\e90c";
    }

    .icon-cancel:before {
        content: "\e90d";
    }

    .icon-cart:before {
        content: "\e90e";
    }

    .icon-check-box:before {
        content: "\e90f";
    }

    .icon-compare-1:before {
        content: "\e910";
    }

    .icon-compare:before {
        content: "\e911";
    }

    .icon-dislike:before {
        content: "\e912";
    }

    .icon-dollar-sign:before {
        content: "\e913";
    }

    .icon-double-arrow:before {
        content: "\e914";
    }

    .icon-download:before {
        content: "\e915";
    }

    .icon-email:before {
        content: "\e916";
    }

    .icon-error:before {
        content: "\e917";
    }

    .icon-eye:before {
        content: "\e918";
    }

    .icon-filter-1:before {
        content: "\e919";
    }

    .icon-filter-fill:before {
        content: "\e91a";
    }

    .icon-filter:before {
        content: "\e91b";
    }

    .icon-flate-rate:before {
        content: "\e91c";
    }

    .icon-Free-Shipping:before {
        content: "\e91d";
    }

    .icon-grid-view-fill:before {
        content: "\e91e";
    }

    .icon-grid-view:before {
        content: "\e91f";
    }

    .icon-hamburger:before {
        content: "\e920";
    }

    .icon-heart-1:before {
        content: "\e921";
    }

    .icon-heart-2:before {
        content: "\e922";
    }

    .icon-heart-fill:before {
        content: "\e923";
    }

    .icon-heart:before {
        content: "\e924";
    }

    .icon-left-arrow:before {
        content: "\e925";
    }

    .icon-like:before {
        content: "\e926";
    }

    .icon-listing-fill:before {
        content: "\e927";
    }

    .icon-listing:before {
        content: "\e928";
    }

    .icon-location:before {
        content: "\e929";
    }

    .icon-minus:before {
        content: "\e92a";
    }

    .icon-more:before {
        content: "\e92b";
    }

    .icon-orders:before {
        content: "\e92c";
    }

    .icon-pen:before {
        content: "\e92d";
    }

    .icon-plus:before {
        content: "\e92e";
    }

    .icon-product:before {
        content: "\e92f";
    }

    .icon-radio-select:before {
        content: "\e930";
    }

    .icon-radio-unselect:before {
        content: "\e931";
    }

    .icon-right-arrow:before {
        content: "\e932";
    }

    .icon-search:before {
        content: "\e933";
    }

    .icon-share:before {
        content: "\e934";
    }

    .icon-sort-1:before {
        content: "\e935";
    }

    .icon-sort-by:before {
        content: "\e936";
    }

    .icon-sort:before {
        content: "\e937";
        color: #7d7d7d;
    }

    .icon-star-fill:before {
        content: "\e938";
    }

    .icon-star:before {
        content: "\e939";
    }

    .icon-support:before {
        content: "\e93a";
    }

    .icon-tick:before {
        content: "\e93b";
    }

    .icon-toast-done:before {
        content: "\e93c";
    }

    .icon-toast-error:before {
        content: "\e93d";
    }

    .icon-toast-exclamation-mark:before {
        content: "\e93e";
    }

    .icon-toast-info:before {
        content: "\e93f";
    }

    .icon-truck:before {
        content: "\e940";
    }

    .icon-uncheck:before {
        content: "\e941";
    }

    .icon-users:before {
        content: "\e942";
    }


    .journal-scroll::-webkit-scrollbar {
        width: 14px;
        cursor: pointer;
    }

    .journal-scroll::-webkit-scrollbar-track {
        background-color: #fff;
        cursor: pointer;
        border-radius: 12px;
        border: 1px solid #e9e9e9;
    }

    .journal-scroll::-webkit-scrollbar-thumb {
        cursor: pointer;
        background-color: #e9e9e9;
        border-radius: 12px;
        border: 3px solid transparent;
        background-clip: content-box;
    }

    .scrollbar-width-hidden {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-width-hidden::-webkit-scrollbar {
        display: none;
    }

    .custom-select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background: transparent;
        background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
        background-repeat: no-repeat;
        background-position-x: calc(100% - 10px);
        background-position-y: 50%;
    }

    [dir="rtl"] .custom-select {
        background-position-x: calc(100% - (100% - 10px));
    }

    * {
        @apply box-border font-sans;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* For IE, Edge and Firefox */
    .scrollbar-hide {
        -ms-overflow-style: none;
        /* IE and Edge */
        scrollbar-width: none;
        /* Firefox */
    }

    .direction-ltr {
        direction: ltr;
    }

    .direction-rtl {
        direction: rtl;
    }

    .label-pending,
    .label-processing,
    .label-closed,
    .label-canceled,
    .label-info,
    .label-completed,
    .label-active {
        @apply text-[12px] text-white font-semibold py-px px-1.5 max-w-max rounded-[35px];
    }

	.label-pending {
		@apply bg-yellow-500;
	}

	.label-processing{
		@apply bg-cyan-600;
    }

	.label-completed,
    .label-active {
		@apply bg-green-600;
	}

	.label-closed {
		@apply bg-indigo-600;
	}

    .label-canceled {
        @apply bg-rose-600;
    }

    .label-info {
        @apply bg-slate-400;
    }

    .primary-button {
        @apply flex gap-x-1.5 items-center place-content-center font-medium rounded-xl text-white bg-navyBlue border border-navyBlue px-8 py-4 max-w-max cursor-pointer transition-all hover:opacity-[0.9];
    }

    .secondary-button {
        @apply flex gap-x-1.5 items-center place-content-center font-medium rounded-xl bg-white text-navyBlue border border-navyBlue px-8 py-4 max-w-max cursor-pointer transition-all hover:bg-[#050e3a0d];
    }

    .shimmer {
        animation-duration: 1.2s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-name: skeleton;
        animation-timing-function: linear;
        background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
        background-size: 1250px 100%;
    }

    @keyframes skeleton {
        0% {
            background-position: -1250px 0;
        }

        100% {
            background-position: 1250px 0;
        }
    }

    @keyframes on-fade {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }

    .wrapper-404 {
        position: relative;
        font-size: 394px;
        perspective: 4.5em;
        overflow: clip;
        place-self: stretch;
        top: 50%;
        transform: translateY(-62%);
    }

    .glow-404,
    .glow-shadow-404 {
        position: absolute;
        inset: 0;
        display: grid;
        place-content: center;
        font-family: 'DM Serif Display';
        overflow: hidden;
        letter-spacing: 24px;
    }

    .glow-404 {
        background-clip: text;
        -webkit-background-clip: text;
        color: #060C3B;
    }

    .glow-shadow-404 {
        color: #E6E7EB;
        mix-blend-mode: darken;
        transform: translateY(15.5%) rotateX(243deg) scaleY(-1) translateY(-23%) scaleY(1.35) translateX(7%) skewX(-45deg);
    }

    canvas[resize] {
        width: 100%;
        height: 100vh;
    }

    html {
        margin-top: 0px !important;
    }

    .break-word-custom {
        word-break: break-word;
    }

    .required:after {
        content: "*";
    }
}
