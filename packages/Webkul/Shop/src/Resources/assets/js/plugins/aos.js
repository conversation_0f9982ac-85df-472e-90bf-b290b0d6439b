import Aos from '/node_modules/aos/dist/aos.js';
import '/node_modules/aos/dist/aos.css';

export default {
    install: (app) => {
        window.Aos = Aos;

        // Fix scroll bounce and DOM bouncing issues
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent scroll bounce on iOS and other mobile devices
            document.body.style.overscrollBehavior = 'none';
            document.documentElement.style.overscrollBehavior = 'none';

            // Fix any flexbox issues that might cause bouncing
            document.body.style.position = 'relative';
            document.body.style.overflow = 'auto';

            // Ensure smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';
        });

        Aos.init({
            duration: 800,
            easing: 'ease-out',
            once: false,
            mirror: false,
            anchorPlacement: 'top-bottom',
            disable: false,
            startEvent: 'DOMContentLoaded',
            initClassName: 'aos-init',
            animatedClassName: 'aos-animate',
            useClassNames: false,
            disableMutationObserver: false,
            debounceDelay: 50,
            throttleDelay: 99
        });

        // Additional scroll bounce prevention
        window.addEventListener('load', function() {
            // Prevent elastic scrolling on mobile
            let isScrolling = false;

            window.addEventListener('scroll', function() {
                if (!isScrolling) {
                    window.requestAnimationFrame(function() {
                        // Ensure we're within valid scroll bounds
                        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
                        const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

                        if (currentScroll < 0) {
                            window.scrollTo(0, 0);
                        } else if (currentScroll > maxScroll) {
                            window.scrollTo(0, maxScroll);
                        }

                        isScrolling = false;
                    });
                }
                isScrolling = true;
            }, { passive: true });
        });
    },
};
