import Aos from '/node_modules/aos/dist/aos.js';
import '/node_modules/aos/dist/aos.css';

export default {
    install: (app) => {
        window.Aos = Aos;
        Aos.init({
            duration: 800,
            easing: 'ease-out',
            once: false,
            mirror: false,
            anchorPlacement: 'top-bottom',
            disable: false,
            startEvent: 'DOMContentLoaded',
            initClassName: 'aos-init',
            animatedClassName: 'aos-animate',
            useClassNames: false,
            disableMutationObserver: false,
            debounceDelay: 50,
            throttleDelay: 99
        });
    },
};
