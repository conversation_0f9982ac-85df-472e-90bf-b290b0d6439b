@php
    $channel = core()->getCurrentChannel();
@endphp

<!-- SEO Meta Content -->
@push ('meta')
    <meta name="title" content="{{ $channel->home_seo['meta_title'] ?? '' }}" />

    <meta name="description" content="{{ $channel->home_seo['meta_description'] ?? '' }}" />

    <meta name="keywords" content="{{ $channel->home_seo['meta_keywords'] ?? '' }}" />
@endPush

<x-shop::layouts>
    <!-- Page Title -->
    <x-slot:title>
        {{  ($channel->home_seo['meta_title'] ?? '') . " | Home" }}
    </x-slot>

    <sprite-scroll></sprite-scroll>

    <!-- Loop over the theme customization -->
    @foreach ($customizations as $customization)
        @php ($data = $customization->options) @endphp

        <!-- Static content -->
        @switch ($customization->type)
            @case ($customization::IMAGE_CAROUSEL)
                <!-- Image Carousel -->
                {{-- <x-shop::carousel :options="$data" aria-label="Image Carousel" /> --}}

                @break
            @case ($customization::STATIC_CONTENT)
                <!-- push style -->
                @if (! empty($data['css']))
                    @push ('styles')
                        <style>
                            {{ $data['css'] }}
                        </style>
                    @endpush
                @endif

                <!-- render html -->
                @if (! empty($data['html']))
                    {!! $data['html'] !!}
                @endif

                @break
            @case ($customization::CATEGORY_CAROUSEL)
                <!-- Categories carousel -->
                <x-shop::categories.carousel
                    :title="$data['title'] ?? ''"
                    :src="route('shop.api.categories.index', [app()->getLocale(), $data['filters'] ?? []])"
                    :navigation-link="route('shop.home.index', app()->getLocale())"
                    aria-label="Categories Carousel"
                />

                @break
            @case ($customization::PRODUCT_CAROUSEL)
                <!-- Product Carousel -->
                <x-shop::products.carousel
                    :title="$data['title'] ?? ''"
                    :src="route('shop.api.products.index', [app()->getLocale(), $data['filters'] ?? []])"
                    :navigation-link="route('shop.search.index', [app()->getLocale(), $data['filters'] ?? []])"
                    aria-label="Product Carousel"
                />

                @break
        @endswitch
    @endforeach

    <!-- Best Sellers -->
    <div id="best-sellers-vue">
      <best-seller-products />
    </div>

    <!-- Our Collections -->
    @if (isset($collection[0]->options) && !empty($collection[0]->options['css']))
        @push ('styles')
            <style>
                {{ $collection[0]->options['css'] }}
            </style>
        @endpush
    @endif

    @if (isset($collection[0]->options) && !empty($collection[0]->options['html']))
        {!! $collection[0]->options['html'] !!}
    @endif

    <!-- Exclusive Privilege -->
    <section
      class="flex items-end justify-center relative bg-primary py-14 px-5"
    >
      <div class="max-w-2xl xl:max-w-[970px] mx-auto text-center">
        <h1
          class="mb-16 md:mb-20 text-center font-playfair font-medium text-secondary text-3xl md:text-4xl xl:text-5xl capitalize leading-[60px]"
          data-aos="fade-up" data-aos-duration="800" data-aos-easing="ease-out"
        >
          @lang('shop::app.home.exclusive-privilege.title')
        </h1>

        <p
          class="text-dark-gray-2 capitalize leading-loose text-lg lg:text-xl xl:text-2xl mb-12"
          data-aos="fade-up" data-aos-duration="800" data-aos-easing="ease-out"
        >
          @lang('shop::app.home.exclusive-privilege.description')
        </p>

        <x-shop::form
            :action="route('shop.subscription.store', app()->getLocale())"
            class="mb-12 max-w-md xl:max-w-[600px] mx-auto"
          >
          <div class="flex items-center flex-col relative">
            <x-shop::form.control-group.control
                type="email"
                class="w-full h-12 xl:h-16 px-6 py-2 xl:pt-3 rounded-full border border-[#707070] focus:outline-none bg-white xl:text-2xl xl:placeholder:text-2xl placeholder:text-dark-gray-2"
                name="email"
                rules="required|email"
                :placeholder="trans('shop::app.home.exclusive-privilege.email')"
            />


            <button
              type="submit"
              class="absolute right-4 flex items-center justify-center inset-y-0 top-1/2 transform -translate-y-1/2"
            >
              <img
                class="w-5 h-5"
                src="{{ asset('widian-assets/images/chevron-right.svg') }}"
                alt="chevron-right"
              />
            </button>
        </div>
        <x-shop::form.control-group.error control-name="email" class="mt-2 text-start" />
      </x-shop::form>

        <p class="text-dark-gray-2 xl:text-lg">
          @lang('shop::app.home.exclusive-privilege.terms-conditions')
        </p>
      </div>
    </section>

    <!-- Best sellers script -->
    @pushOnce('scripts')
      <script type="text/x-template" id="v-best-seller-products-template">
        <section v-if="products.length > 0" class="pt-24 bg-primary">
          <div class="text-center mb-[60px]">
            <h2 class="uppercase text-black font-playfair font-medium leading-relaxed text-xl mb-5" data-aos="fade-up" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="100">
              @lang('shop::app.categories.our-products')
            </h2>

            <h1 class="text-black text-3xl md:text-5xl uppercase" data-aos="fade-up" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="200">
              @lang('shop::app.categories.best-sellers')
            </h1>
          </div>

          <div class="product-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 max-w-[1920px] mx-auto">
            <template v-if="isLoadingBestSeller">
              <x-shop::shimmer.products.cards.grid count="4" />
            </template>
            <template v-else>
              <div v-for="product in products" :key="product.id" class="p-5 text-center product-card relative group">
                <span v-if="product.is_new" class="absolute w-12 h-6 flex items-center justify-center pt-[3px] top-5 left-5 bg-secondary text-white text-xs font-bold rounded-full z-10">
                  @lang('shop::app.categories.new')
                </span>

                <ul class="absolute right-5 top-5 space-y-3 hidden group-hover:block z-10">
                  <button @click="quickViewProduct(product)" class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer">
                    <img src="{{ asset('widian-assets/images/quick-view.svg') }}" alt="quick-view" />
                  </button>

                  @if (core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.before') !!}

                    <button class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer"
                      :disabled="! product.is_saleable || isAddingToCart"
                      @click="addToCartFromHome(product)"
                      >
                      <img src="{{ asset('widian-assets/images/cart-icon.svg') }}" alt="cart" />
                    </button>

                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.after') !!}
                  @endif
                </ul>

                <div class="h-[264px] sm:h-[459px] relative w-full overflow-hidden mt-8">
                  <img
                    :src="product.cover_images[0]?.url ?? product.base_image.original_image_url"
                    :alt="product.name"
                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-100 group-hover:opacity-0"
                  />

                  <img
                    :src="product.hover_images[0]?.url ?? product.base_image.original_image_url"
                    :alt="product.name"
                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-0 group-hover:opacity-100"
                  />
                </div>

                <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)" class="text-center text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-loose tracking-wider" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="400">
                  @{{ product.name }}
                </a>

                <div class="flex items-center justify-center gap-3 flex-wrap" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="500">
                  <p class="text-center text-sm md:text-base text-[#3D3C3A] font-normal leading-snug tracking-wide">
                    @{{ product.prices.final.formatted_price }}
                  </p>
                  <p v-if="product.prices.regular.formatted_price != product.prices.final.formatted_price" class="line-through text-center text-xs md:text-sm opacity-50 text-[#3D3C3A] font-normal leading-snug tracking-wide">
                    @{{ product.prices.regular.formatted_price }}
                  </p>
                </div>

              </div>
            </template>
          </div>
          <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
              <form ref="formData" @submit="handleSubmit($event, addToCartFromQuickView)">
                  <x-shop::modal ref="quickViewModal">
                      <x-slot:header class="!justify-end !p-4"></x-slot:heade>
                      <x-slot:content class="!px-4">
                          <div v-if="currentQuickViewProduct" class="flex flex-col sm:flex-row gap-3 sm:gap-5 items-start mb-5">
                              <x-shop::form.control-group.control type="hidden" name="product_id" ::value="currentQuickViewProduct.id" />
                              <div class="w-full sm:w-1/3 h-52">
                                  <img :src="currentQuickViewProduct.base_image.original_image_url" :alt="currentQuickViewProduct.name" class="mx-auto w-full h-full object-contain" />
                              </div>

                              <div class="w-full sm:w-2/3 text-center sm:text-start">
                                  <div class="flex items-center gap-3 mb-3">
                                    <h1 class="text-secondary text-xl xl:text-2xl font-medium font-playfair">@{{ currentQuickViewProduct.name }}</h1>
                                    <p v-if="currentQuickViewProduct.category" class="text-black text-md xl:text-lg font-normal mt-2">@{{ currentQuickViewProduct.category?.name }}</p>
                                  </div>
                                  <div class="flex items-center justify-center sm:justify-start gap-5 pb-2">
                                      <span class="text-black text-md xl:text-lg font-normal capitalize">@{{ currentQuickViewProduct.prices.final.formatted_price }}</span>
                                      <span v-if="currentQuickViewProduct.prices.regular.formatted_price != currentQuickViewProduct.prices.final.formatted_price" class="opacity-50 text-black text-lg xl:text-base font-normal line-through capitalize">@{{ currentQuickViewProduct.prices.regular.formatted_price }}</span>
                                  </div>

                                  <div v-if="currentQuickViewProduct.product_size && currentQuickViewProduct.product_size.admin_name" class="mt-2.5">
                                      <div class="flex flex-wrap gap-5">
                                          <div class="text-black text-sm font-normal p-2 border border-black">@{{ currentQuickViewProduct.product_size.admin_name }} @lang('shop::app.products.view.extrait-de-parfum')</div>
                                      </div>
                                  </div>
                                  <x-shop::quantity-changer ::max="currentQuickViewProduct.stock_qty" name="quantity" ::value="1" class="mt-5 inline-flex border border-light-gray"/>

                              </div>
                          </div>
                          <div class="border-light-gray border-t pt-5">
                            <button type="submit" class="bg-black hover:bg-secondary duration-300 flex items-center justify-center w-full text-white text-lg font-bold uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12">
                              <span class="mt-1">@lang('shop::app.products.view.add-to-cart')</span>
                            </button>
                          </div>
                      </x-slot>
                  </x-shop::modal>
              </form>
          </x-shop::form>
        </section>
      </script>

      <script type="text/x-template" id="v-sprite-scroll-template">
          <div class="relative" style="height: 3000px;">
            <div id="scrolly-video"></div>
          </div>
      </script>

      <script type="module">
        app.component('best-seller-products', {
            template: '#v-best-seller-products-template',

            data() {
                return {
                    isLoadingBestSeller: true,
                    products: [],
                    isAddingToCart: false,
                    currentQuickViewProduct: null,
                };
            },

            mounted() {
                this.getProducts();

                // Refresh AOS when component is mounted
                this.$nextTick(() => {
                    setTimeout(() => {
                        if (window.Aos) {
                            window.Aos.refresh();
                        }
                        if (window.refreshAOS) {
                            window.refreshAOS();
                        }
                    }, 200);
                });
            },

            updated() {
                // Refresh AOS after component updates to ensure animations work properly
                this.$nextTick(() => {
                    if (window.Aos) {
                        window.Aos.refresh();
                    }
                    // Also call global refresh method if available
                    if (window.refreshAOS) {
                        window.refreshAOS();
                    }
                });
            },

            methods: {
              getProducts() {
                  this.$axios.get(`{{ route('shop.api.products.best-seller-products', app()->getLocale())}}`)
                      .then(response => {
                          this.isLoadingBestSeller = false;

                          this.products = response.data.data;

                          // Refresh AOS after products are loaded to ensure animations work
                          this.$nextTick(() => {
                              // Add a small delay to ensure DOM is fully rendered
                              setTimeout(() => {
                                  if (window.Aos) {
                                      window.Aos.refresh();
                                  }
                                  // Also call global refresh method if available
                                  if (window.refreshAOS) {
                                      window.refreshAOS();
                                  }
                              }, 100);
                          });
                      }).catch(error => {
                          console.log(error);
                      });
              },

              addToCart(params) {
                  this.isAddingToCart = true;

                  this.$axios.post('{{ route("shop.api.checkout.cart.store", app()->getLocale()) }}', params)
                      .then(response => {
                          if (response.data.message) {
                              this.$emitter.emit('update-mini-cart', response.data.data );

                              this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                          } else {
                              this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                          }

                          this.isAddingToCart = false;
                      })
                      .catch(error => {
                          this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                          if (error.response.data.redirect_uri) {
                              window.location.href = error.response.data.redirect_uri;
                          }

                          this.isAddingToCart = false;
                      });
              },

              addToCartFromHome(product) {
                const params = {
                  'quantity': 1,
                  'product_id': product.id,
                };

                this.addToCart(params);
              },

              addToCartFromQuickView(data) {
                const params = {
                  quantity: this.$refs.formData.quantity.value,
                  product_id: this.$refs.formData.product_id.value,
                };

                this.addToCart(params);
                this.$refs.formData.reset();
                this.$refs.quickViewModal.close();
              },

              quickViewProduct(product) {
                this.currentQuickViewProduct = product;
                this.$refs.quickViewModal.open();
              },
            },
        });

        app.component('sprite-scroll', {
          template: '#v-sprite-scroll-template',

          mounted() {
            new ScrollyVideo({
              scrollyVideoContainer: "scrolly-video",
              src: "{{ asset('widian-assets/videos/Harrods-3D-Video.mp4') }}"
            });
          },

          methods: {
            throttle(fn, wait) {
              let lastCall = 0
              return (...args) => {
                const now = Date.now()
                if (now - lastCall >= wait) {
                  lastCall = now
                  return fn(...args)
                }
              }
            },
          },
        })
      </script>
    @endPushOnce
</x-shop::layouts>
