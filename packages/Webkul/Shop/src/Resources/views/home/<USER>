@inject('categoryRepository', 'Webkul\Category\Repositories\CategoryRepository')

<x-shop::layouts>
    @php
        $emotions = $categoryRepository->getVisibleCategoryTree(null, 1);
    @endphp

    <x-slot:title>
        @lang('shop::app.home.shop-by-emotion.title')
    </x-slot>

    {!! $page->html_content !!}

    @foreach ($emotions as $key => $emotion)
        @php
            $emotionProducts = Webkul\Shop\Http\Resources\ProductResource::collection($emotion->products);
        @endphp

        <section>
            <div class="flex flex-col md:flex-row items-stretch">
                <!-- Left Content -->
                <div class="banner-image relative w-full md:w-[55%] flex items-center bg-cover bg-center {{ $loop->even ? 'order-2' : 'md:order-1 order-2' }}" style="background-image: url({{ $emotion->featured_image_url }});">
                    <!-- products slides -->
                    <div class="relative max-w-[90%] w-[360px] mx-auto my-20 xl:my-[120px]">
                        <v-emotion-related-products :products="{{ json_encode($emotionProducts) }}" />
                        <!-- Arrow -->
                        <button class="bg-white max-sm:w-8 max-sm:h-8 w-11 h-11 rounded-full flex items-center justify-center absolute right-[-15px] md:right-[-20px] lg:right-[-60px] bottom-[30%] md:bottom-[20%]">
                            <svg width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" />
                            </svg>
                        </button>
                        <button class="bg-white max-sm:w-8 max-sm:h-8 w-11 h-11 rounded-full flex items-center justify-center absolute left-[-15px] md:left-[-20px] lg:left-[-60px] bottom-[30%] md:bottom-[20%]">
                            <svg class="rotate-180" width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Right Content -->
                <div id="emotion-section-{{ $loop->index }}" class="emotion-content w-full md:w-[45%] flex items-center bg-[{{ $emotion->emotion_color }}] {{ $loop->even ? 'order-1' : 'md:order-2 order-1' }}">
                    <div class="max-xl:px-5 max-md:pt-8 w-full md:max-w-[460px] 2xl:max-w-[520px] text-center md:text-{{ $loop->even ? 'start' : 'end' }} mx-auto my-20 md:my-0">
                        <h2 class="text-white font-playfair text-4xl 2xl:text-5xl font-normal mb-6 uppercase" data-aos="fade-{{ $loop->even ? 'right' : 'left' }}" data-aos-offset="300" data-aos-duration="600" data-aos-anchor="#emotion-section-{{ $loop->index }}" data-aos-delay="100">
                            {{ $emotion->name }}
                        </h2>
                        <span class="text-white uppercase text-xl md:text-2xl mb-6 block" data-aos="fade-{{ $loop->even ? 'right' : 'left' }}" data-aos-offset="250" data-aos-duration="600" data-aos-anchor="#emotion-section-{{ $loop->index }}" data-aos-delay="200">
                            {{ $emotion->header }}
                        </span>
                        <div data-aos="fade-{{ $loop->even ? 'right' : 'left' }}" data-aos-offset="200" data-aos-duration="600" data-aos-anchor="#emotion-section-{{ $loop->index }}" data-aos-delay="300">
                            {!! $emotion->description !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endforeach

    @push('scripts')
        <script type="text/x-template" id="v-emotion-related-products-template">
            <div class="product-carousel owl-theme owl-carousel">
                <div class="md:flex flex-col w-full max-w-[480px] group" v-for="product in products">
                    <img class="relative max-sm:w-[200px] max-sm:h-[350px] max-w-[380px] max-h-[520px] object-contain z-10 mx-auto mb-[-45%]" :src="product?.emotion_image?.url" :alt="product.name" />
                    <div class="bg-white rounded-[20px] flex flex-col items-center grow relative pt-[162px] relative mb-[26px]">
                        <h2 class="text-center text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-loose tracking-wider mb-2">
                            @{{ product.name }}
                        </h2>
                        <p class="text-center text-sm md:text-base text-[#3D3C3A] font-normal leading-snug tracking-wide mb-[34px]">
                            @{{ product?.prices?.final?.formatted_price }}
                        </p>
                        <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)" class="max-sm:text-sm uppercase px-5 sm:px-9 py-2.5 rounded-full bg-secondary text-white border border-secondary hover:bg-white hover:text-dark-gray tansition-all ease-in-out duration-500 -mb-6 bottom-0 relative group-hover:bottom-3">
                            <span class="inline-flex">@lang('shop::app.home.shop-by-emotion.discover-fragrance')</span>
                        </a>
                        <ul class="absolute right-0 top-3 opacity-0 invisible space-y-[10px] group-hover:visible group-hover:opacity-100 ease-in-out duration-500 tansition-all ease-in-out duration-500 group-hover:right-3 z-[999]">
                            <li @click="quickViewProduct(product)" class="w-9 h-9 bg-secondary rounded-full flex justify-center items-center cursor-pointer">
                                <img class="!w-4" src="{{ asset('widian-assets/images/quick-view.svg') }}" alt="quick-view" />
                            </li>
                            <li @click="addToCartFromShopByEmotion(product)" :disabled="isAddingToCart" class="w-9 h-9 bg-secondary rounded-full flex justify-center items-center cursor-pointer">
                                <img class="!w-4" src="{{ asset('widian-assets/images/cart-icon.svg') }}" alt="cart-icon" />
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
              <form ref="formData" @submit="handleSubmit($event, addToCartFromQuickView)">
                  <x-shop::modal ref="quickViewModal">
                      <x-slot:header class="!justify-end !p-4"></x-slot:heade>
                      <x-slot:content class="!px-4">
                          <div v-if="currentQuickViewProduct" class="flex flex-col sm:flex-row gap-3 sm:gap-5 items-start mb-5">
                              <x-shop::form.control-group.control type="hidden" name="product_id" ::value="currentQuickViewProduct.id" />
                              <div class="w-full sm:w-1/3 h-52">
                                  <img :src="currentQuickViewProduct?.emotion_image?.url" :alt="currentQuickViewProduct.name" class="mx-auto w-full h-full object-contain" />
                              </div>

                              <div class="w-full sm:w-2/3 text-center sm:text-start">
                                  <div class="flex items-center gap-3 mb-3">
                                    <h1 class="text-secondary text-xl xl:text-2xl font-medium font-playfair">@{{ currentQuickViewProduct.name }}</h1>
                                    <p v-if="currentQuickViewProduct.category" class="text-black text-md xl:text-lg font-normal mt-2">@{{ currentQuickViewProduct.category?.name }}</p>
                                  </div>
                                  <div class="flex items-center justify-center sm:justify-start gap-5 pb-2">
                                      <span class="text-black text-md xl:text-lg font-normal capitalize">@{{ currentQuickViewProduct.prices.final.formatted_price }}</span>
                                      <span v-if="currentQuickViewProduct.prices.regular.formatted_price != currentQuickViewProduct.prices.final.formatted_price" class="opacity-50 text-black text-lg xl:text-base font-normal line-through capitalize">@{{ currentQuickViewProduct.prices.regular.formatted_price }}</span>
                                  </div>

                                  <div v-if="currentQuickViewProduct.product_size && currentQuickViewProduct.product_size.admin_name" class="mt-2.5">
                                      <div class="flex flex-wrap gap-5">
                                          <div class="text-black text-sm font-normal p-2 border border-black">@{{ currentQuickViewProduct.product_size.admin_name }} @lang('shop::app.products.view.extrait-de-parfum')</div>
                                      </div>
                                  </div>
                                  <x-shop::quantity-changer ::max="currentQuickViewProduct.stock_qty" name="quantity" ::value="1" class="mt-5 inline-flex border border-light-gray"/>

                              </div>
                          </div>
                          <div class="border-light-gray border-t pt-5">
                            <button type="submit" class="bg-black hover:bg-secondary duration-300 flex items-center justify-center w-full text-white text-lg font-bold uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12">
                              <span class="mt-1">@lang('shop::app.products.view.add-to-cart')</span>
                            </button>
                          </div>
                      </x-slot>
                  </x-shop::modal>
              </form>
          </x-shop::form>
        </script>

        <script type="module">
            app.component('v-emotion-related-products', {
                template: '#v-emotion-related-products-template',

                props: {
                    products: {
                        type: Array,
                        required: true
                    },
                    isEven: {
                        type: Boolean,
                        required: true
                    }
                },

                data() {
                    return {
                        isAddingToCart: false,
                        currentQuickViewProduct: null,
                    }
                },

                mounted() {
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.initProductCarousel();

                            // Refresh AOS for individual section animations
                            if (window.Aos) {
                                window.Aos.refresh();
                            }
                            if (window.refreshAOS) {
                                window.refreshAOS();
                            }
                        }, 300);
                    });
                },

                methods: {
                    addToCart(params) {
                        this.isAddingToCart = true;

                        this.$axios.post('{{ route('shop.api.checkout.cart.store', app()->getLocale()) }}', params)
                            .then(response => {
                                if (response.data.message) {
                                    this.$emitter.emit('update-mini-cart', response.data.data);

                                    this.$emitter.emit('add-flash', {
                                        type: 'success',
                                        message: response.data.message
                                    });
                                } else {
                                    this.$emitter.emit('add-flash', {
                                        type: 'warning',
                                        message: response.data.data.message
                                    });
                                }

                                this.isAddingToCart = false;
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: error.response.data.message
                                });

                                if (error.response.data.redirect_uri) {
                                    window.location.href = error.response.data.redirect_uri;
                                }

                                this.isAddingToCart = false;
                            });
                    },

                    initProductCarousel() {
                        this.destroyAllCarousels();

                        const $carousel = $('.product-carousel');
                        if ($carousel.length && $carousel.children().length > 0) {
                            $carousel.owlCarousel({
                                loop: true,
                                margin: 30,
                                nav: true,
                                autoplay: false,
                                mouseDrag: true,
                                dots: false,
                                items: 1,
                                navText: [
                                    '<p class="bg-white max-sm:w-8 max-sm:h-8 w-11 h-11 rounded-full flex items-center justify-center"><svg class="rotate-180" width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg></p>',
                                    '<p class="bg-white max-sm:w-8 max-sm:h-8 w-11 h-11 rounded-full flex items-center justify-center"><svg width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg></p>'
                                ]
                            });
                        }
                    },

                    destroyAllCarousels() {
                        $('.product-carousel').each(function() {
                            if ($(this).hasClass('owl-loaded')) {
                                $(this).trigger('destroy.owl.carousel')
                                    .removeClass('owl-loaded owl-hidden')
                                    .find('.owl-stage-outer').children().unwrap();
                            }
                        });
                    },

                    addToCartFromShopByEmotion(product) {
                        const params = {
                          quantity: 1,
                          product_id: product.id,
                        };

                        this.addToCart(params);
                    },

                    addToCartFromQuickView(data) {
                        const params = {
                          quantity: this.$refs.formData.quantity.value,
                          product_id: this.$refs.formData.product_id.value,
                        };

                        this.addToCart(params);
                        this.$refs.formData.reset();
                        this.$refs.quickViewModal.close();
                    },

                    quickViewProduct(product) {
                        this.currentQuickViewProduct = product;
                        this.$refs.quickViewModal.open();
                    },
                },
            });
        </script>
    @endPush
</x-shop::layouts>
