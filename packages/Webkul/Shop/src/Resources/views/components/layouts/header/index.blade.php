@inject('themeCustomizationRepository', 'Webkul\Theme\Repositories\ThemeCustomizationRepository')

@php
    $channel = core()->getCurrentChannel();

    $collections = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'name' => 'Collections',
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);

    $collections = isset($collections) && !empty($collections) && isset($collections->options['column_1']) && !empty($collections->options['column_1']) ? $collections->options['column_1'] : [];
@endphp

{!! view_render_event('bagisto.shop.layout.header.before') !!}

@if(core()->getCurrentChannel()->locales()->count() > 1 || core()->getCurrentChannel()->currencies()->count() > 1 )
    <div class="z-40 top-header sticky top-0">
        <x-shop::layouts.header.desktop.top />
    </div>
@endif
<header class="z-40 bg-[#fdf9f2] sticky top-[45px]">
    <x-shop::layouts.header.desktop />
      <div class="max-lg:hidden lg:!flex flex-wrap justify-center sm:px-10 px-4 py-2 relative">
        <div class="max-lg:before:fixed max-lg:before:bg-black max-lg:before:opacity-40 max-lg:before:inset-0 max-lg:before:z-50">
          <v-header-categories />
        </div>
      </div>
    <x-shop::layouts.header.mobile />

    <div id="searchBar" class="absolute w-full px-5 sm:px-10 2xl:px-20 bg-primary max-h-0 overflow-hidden opacity-0 transition-all duration-500 ease-in-out pointer-events-none">

        <div class="w-full mx-auto flex gap-[10px] border-b border-light-gray my-[10px]">
            <button class="size-[40px] sm:size-[52px] flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
                    <path d="M20 18.2909L14.8664 13.1564C15.7696 11.8181 16.2515 10.24 16.25 8.62545C16.25 4.14545 12.6064 0.5 8.12545 0.5C3.64545 0.5 0 4.14545 0 8.62545C0 13.1064 3.64545 16.7509 8.12545 16.7509C9.74009 16.7528 11.3183 16.2709 12.6564 15.3673L17.7891 20.5L20 18.2909ZM8.12545 14.25C6.63393 14.2486 5.20392 13.6554 4.14925 12.6007C3.09459 11.5461 2.50144 10.1161 2.5 8.62455C2.50168 7.1331 3.09491 5.70322 4.14952 4.64861C5.20413 3.594 6.63401 3.00078 8.12545 2.99909C9.6169 3.00078 11.0468 3.594 12.1014 4.64861C13.156 5.70322 13.7492 7.1331 13.7509 8.62455C13.7492 10.116 13.156 11.5459 12.1014 12.6005C11.0468 13.6551 9.6169 14.2483 8.12545 14.25Z" fill="#CCCCCC"></path>
                </svg>
            </button>

            <div class="flex w-full">
                <input oninput="searchProducts(this.value)" id="search-box" type="text" class="text-[20px] text-dark-gray outline-none bg-transparent grow">
                <button class="size-[40px] sm:size-[52px] flex items-center justify-center bg-primary-dark" onclick="toggleSearchBar()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17" fill="none">
                        <path d="M1.8949 1.3949L16.1051 15.6051M16.1051 1.3949L1.8949 15.6051" stroke="#3D3C3A" stroke-width="2.52055" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
            </div>
        </div>

        <section class="bg-primary w-full">
            <div id="no-search-result-container" class="mt-3"></div>
            <div class="mx-auto pb-20 pt-10 py-[50px] flex slef-streach gap-5  xl:gap-[64px] px-5 sm:px-10 2xl:px-20">
                <div class="max-md:hidden w-full max-w-[250px] xl:max-w-[368px] border-e border-light-gray">
                    <h4 class="font-bold text-lg py-[10px] text-dark-gray mb-5">@lang('shop::app.components.layouts.header.search-by-collection')</h4>
                    <ul class="text-dark-gray">
                        @foreach ($collections as $collection)
                            @if (isset($collection['title']) && !empty($collection['title']))
                                <li><a href="{{ $collection['url'] }}" class="inline-block text-sm pb-5 capitalize">{{ $collection['title'] }}</a></li>
                            @endif
                        @endforeach
                    </ul>
                </div>

                <section class="w-full max-h-[500px] overflow-auto">
                    <search-filter-products />
                </section>
            </div>
        </section>
    </div>
</header>

<div class="fixed bg-[#757B584D] inset-0 z-30 hidden" id="searchBarOverlay"></div>

{!! view_render_event('bagisto.shop.layout.header.after') !!}

@pushOnce('scripts')
    <script type="text/x-template" id="v-header-categories-template">
        <div>
            <div v-if="isLoading" class="text-center text-gray-400 py-4">
                <span class="shimmer h-6 w-20 rounded" role="presentation"></span>
                <span class="shimmer h-6 w-20 rounded" role="presentation"></span>
                <span class="shimmer h-6 w-20 rounded" role="presentation"></span>
            </div>

            <div
                class="flex items-center"
                v-else
            >
                <div
                    class="relative flex items-center mx-4"
                    v-if="categories.length"
                    v-for="category in categories"
                >
                    <div
                        @mouseenter="openedSubMenu = category"
                        @mouseleave="openedSubMenu = null"
                    >
                        <a
                            :href="category.url"
                            class="text-sm font-medium tracking-widest uppercase"
                        >
                            @{{ category.title }}
                        </a>

                        <div v-if="category?.sub_menus?.length && openedSubMenu?.title == category.title" class="popup absolute top-5 left-0 z-30">
                            <div class="inner mt-2.5">
                                <ul class="menu min-w-[190px] py-1.5 border-t-4 border-secondary bg-white">
                                    <li class="menu-item bg-white hover:bg-primary" v-for="sub_menu in category?.sub_menus">
                                        <a :href="sub_menu.url" class="text-secondary font-normal text-sm px-4 py-2 w-full block">
                                            @{{ sub_menu.title }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="text/x-template" id="v-search-filter-products-template">
        <template v-if="isProductLoading">
            <div class="grid grid-cols-2 sm:grid-cols-2  md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 mb-[50px] gap-5 border-light-gray">
                <x-shop::shimmer.products.cards.grid count="4" />
            </div>
        </template>
        <template v-else-if="products.length > 0">
            <div class="grid grid-cols-2 sm:grid-cols-2  md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 mb-[50px] gap-5 border-light-gray">
                <div v-for="(product, index) in products.slice(0, 4)" :key="product.id" class="p-5 text-center bg-primary relative group border border-light-gray">
                    <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)">
                        <img :src="product.base_image.original_image_url" :alt="product.name" class="mx-auto aspect-[10/9] w-full object-contain"/>
                    </a>
                    <h2 class="text-center text-black text-base font-normal font-playfair uppercase leading-loose tracking-wider pt-5">@{{ product.name }}</h2>
                </div>
            </div>

            <div v-if="products.length > 4" class="flex items-center justify-center">
                <a :href="query ? '{{ route('shop.search.index', app()->getLocale()) }}?q=' + query : '{{ route('shop.search.index', app()->getLocale()) }}'" class="min-w-[150px] rounded-full border border-black py-[10px] text-center leading-[22px] transition-all duration-300 hover:border-transparent hover:bg-secondary hover:text-white">
                    @lang('shop::app.components.layouts.header.show-all')
                </a>
            </div>
        </template>
    </script>

    <script type="module">
        app.component('v-header-categories', {
            template: '#v-header-categories-template',

            data() {
                return {
                    isLoading: true,
                    categories: [],
                    openedSubMenu: null,
                    query: ''
                }
            },

            mounted() {
                this.get();
            },

            methods: {
                get() {
                    this.$axios.get("{{ route('shop.api.menu-categories.tree', app()->getLocale()) }}")
                        .then(response => {
                            this.isLoading = false;

                            this.categories = response.data.data;
                        })
                        .catch(error => {
                            console.log(error);
                        });
                },
            },
        });

        app.component('search-filter-products', {
            template: '#v-search-filter-products-template',

            data() {
                return {
                    products: [],
                    isProductLoading: false,
                };
            },

            mounted() {
                this.getProducts();

                window.addEventListener('product-search', (e) => {
                    if(e.detail && e.detail.trim().length !== 0){
                        this.query = e.detail;
                        this.searchProducts(e.detail);
                    } else {
                        this.getProducts();
                    }
                });
            },

            methods: {
                getProducts() {
                    this.isProductLoading = true;
                    this.$axios.get(`{{ route('shop.api.products.best-seller-products', app()->getLocale()) }}`)
                        .then(response => {
                            this.products = response.data.data;
                            this.isProductLoading = false;
                        }).catch(error => {
                            console.log(error);
                        });
                },

                searchProducts(searchTerm) {
                    this.isProductLoading = true;

                    this.$axios.get("{{ route('shop.api.products.index', app()->getLocale()) }}", {
                        params: {
                            query: searchTerm,
                            channel_id: "{{ core()->getCurrentChannel()->id }}",
                            status: 1,
                            visible_individually: 1
                        }
                    }).then(response => {
                        this.products = response.data.data;
                        this.isProductLoading = false;

                        if(this.products.length == 0){
                            const searchMessage = `No results found for “${searchTerm}”. Check the spelling or use a different word or phrase.`;
                            const searchContainer = document.getElementById('no-search-result-container');

                            searchContainer.innerHTML = `
                                <button id="clear-btn" class="underline">Clear</button>
                                <div>${searchMessage}</div>
                            `;

                            document.getElementById('clear-btn').addEventListener('click', () => {
                                document.getElementById('search-box').value = "";
                                searchContainer.innerHTML = "";
                            });
                            this.getProducts();
                        }
                    }).catch(error => {
                        console.error(error);
                        this.isProductLoading = false;
                    });
                }
            },
        });
    </script>

    <script>
        function toggleSearchBar() {
            const container = document.getElementById('searchBar');

            if (container.classList.contains('opacity-0')) {
                container.classList.remove('opacity-0', 'max-h-0', 'pointer-events-none');
                container.classList.add('opacity-100', 'max-h-screen');
                document.getElementById('searchBarOverlay').classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            } else {
                container.classList.remove('opacity-100', 'max-h-screen');
                container.classList.add('opacity-0', 'max-h-0', 'pointer-events-none');
                document.getElementById('searchBarOverlay').classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }

        function debounce(func, delay) {
            let timer;
            return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => {
                    func.apply(this, args);
                }, delay);
            };
        }

        const searchProducts = debounce(function (params) {
            document.getElementById('no-search-result-container').innerText = "";
            window.dispatchEvent(new CustomEvent('product-search', {
                detail: params
            }));
        }, 300);

        function handleScroll() {
            const header = document.querySelector('header');
            const topHeader = document.querySelector('.top-header');
            const scrollyVideo = document.querySelector('#scrolly-video');
            const logo = document.querySelector('.widian-header-logo');

            if (window.scrollY > 0) {
                header.classList.add('scrolled');
                topHeader.classList.add('scrolled');
                scrollyVideo ? scrollyVideo.style.top = `36px` : null;
            } else {
                header.classList.remove('scrolled');
                topHeader.classList.remove('scrolled');
                scrollyVideo ? scrollyVideo.style.top = `${header.offsetHeight}px` + `${topHeader.offsetHeight}px` : null;

                // Ensure logo is visible when at top and refresh AOS
                if (logo) {
                    logo.style.visibility = 'visible';
                    logo.style.opacity = '1';

                    // Refresh AOS to ensure logo animation works
                    setTimeout(() => {
                        if (window.Aos) {
                            window.Aos.refresh();
                        }
                        if (window.refreshAOS) {
                            window.refreshAOS();
                        }
                    }, 100);
                }
            }
        }

        // Enhanced initialization
        function initializeHeader() {
            const logo = document.querySelector('.widian-header-logo');

            // Ensure logo is visible on page load
            if (logo) {
                logo.style.visibility = 'visible';
                logo.style.opacity = '1';
            }

            // Initial scroll handling
            handleScroll();

            // Refresh AOS after DOM is ready
            setTimeout(() => {
                if (window.Aos) {
                    window.Aos.refresh();
                }
                if (window.refreshAOS) {
                    window.refreshAOS();
                }
            }, 200);
        }

        window.addEventListener('scroll', handleScroll);

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeHeader);
        } else {
            initializeHeader();
        }
    </script>
@endPushOnce
