@props([
    'hasHeader'  => true,
    'hasFeature' => true,
    'hasFooter'  => true,
])

<!DOCTYPE html>

<html
    lang="{{ app()->getLocale() }}"
    dir="{{ core()->getCurrentLocale()->direction }}"
>
    <head>

        {!! view_render_event('bagisto.shop.layout.head.before') !!}

        <title>{{ $title ?? '' }}</title>

        <meta charset="UTF-8">

        <meta
            http-equiv="X-UA-Compatible"
            content="IE=edge"
        >
        <meta
            http-equiv="content-language"
            content="{{ app()->getLocale() }}"
        >

        <meta
            name="viewport"
            content="width=device-width, initial-scale=1"
        >
        <meta
            name="base-url"
            content="{{ url()->to('/') }}"
        >
        <meta
            name="currency"
            content="{{ core()->getCurrentCurrency()->toJson() }}"
        >

        @stack('meta')

        <link rel="manifest" href="{{ asset('manifest.json') }}">

        <link
            rel="icon"
            sizes="16x16"
            href="{{ core()->getCurrentChannel()->favicon_url ?? bagisto_asset('images/favicon.ico') }}"
        />

        @bagistoVite(['src/Resources/assets/css/app.css', 'src/Resources/assets/js/app.js'])

        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
        >

        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
        >

        <link
            href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap"
            rel="stylesheet"
        />

        <link href="https://fonts.googleapis.com/css2?family=Helvetica:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap" rel="stylesheet">

        <!-- owl carousel -->
        <link rel="stylesheet" href="{{ asset('widian-assets/css/owl-carousel/owl.carousel.min.css')}}">
        <link rel="stylesheet" href="{{ asset('widian-assets/css/owl-carousel/owl.theme.default.min.css')}}">

        @stack('styles')

        <style>
            {!! core()->getConfigData('general.content.custom_scripts.custom_css') !!}
        </style>

        <link rel="stylesheet" href="{{ asset('widian-assets/css/custom.css') }}">

        {!! view_render_event('bagisto.shop.layout.head.after') !!}

    </head>

    <body class="overflow-x-hidden">
        {!! view_render_event('bagisto.shop.layout.body.before') !!}

        <div id="app">
            <!-- Flash Message Blade Component -->
            <x-shop::flash-group />

            <!-- Confirm Modal Blade Component -->
            <x-shop::modal.confirm />

            <!-- Page Header Blade Component -->
            @if ($hasHeader)
                <x-shop::layouts.header />
            @endif

            {!! view_render_event('bagisto.shop.layout.content.before') !!}

            <!-- Page Content Blade Component -->
            <main id="main" class="bg-white">
                {{ $slot }}
            </main>

            {!! view_render_event('bagisto.shop.layout.content.after') !!}


            <!-- Page Services Blade Component -->
            @if ($hasFeature)
                <x-shop::layouts.services />
            @endif

            <!-- Page Footer Blade Component -->
            @if ($hasFooter)
                <x-shop::layouts.footer />
            @endif

             <!-- Arrow Up -->
            <button
                onclick="scrollToTop()"
                id="scroll-to-top"
                class="scroll-to-top fixed w-9 h-9 bg-white bottom-6 right-6 xl:bottom-12 xl:left-9 rounded-full shadow-md z-50 transition-opacity duration-300"
                style="display: none; align-items: center; justify-content: center;"
            >
                <img src="{{ asset('widian-assets/images/arrow-up.svg') }}" alt="arrow-up" />
            </button>
        </div>

        {!! view_render_event('bagisto.shop.layout.body.after') !!}

        {!! view_render_event('bagisto.shop.layout.vue-app-mount.before') !!}
        <script>
            /**
             * Load event, the purpose of using the event is to mount the application
             * after all of our `Vue` components which is present in blade file have
             * been registered in the app. No matter what `app.mount()` should be
             * called in the last.
             */
            window.addEventListener("load", function (event) {
                app.mount("#app");
            });
        </script>

        {!! view_render_event('bagisto.shop.layout.vue-app-mount.after') !!}

        @stack('scripts')

        <script type="text/javascript">
            {!! core()->getConfigData('general.content.custom_scripts.custom_javascript') !!}
        </script>

        <script src="{{ asset('widian-assets/js/owl-carousel/jquery.min.js')}}"></script>
        <script src="{{ asset('widian-assets/js/owl-carousel/owl.carousel.min.js')}}"></script>

        <script src="{{ asset('widian-assets/js/tailwind-css.js') }}"></script>

        <script src="https://www.youtube.com/iframe_api"></script>

        <script>
            // scroll to top
            function scrollToTop() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }

            window.addEventListener('load', function () {
                var youtubeIframes = document.querySelectorAll("iframe[data-video]");

                window.addEventListener('resize', function () {
                    document.querySelectorAll(".social-video-icon.second").forEach(el => el.click());
                    resizeYouTubePlayer(youtubeIframes, 16 / 9);
                });

                const scrollToTopButton = document.getElementById('scroll-to-top');

                if (!scrollToTopButton) {
                    return;
                }

                // Scroll event
                window.addEventListener('scroll', function () {
                    const scrollY = window.scrollY || document.documentElement.scrollTop;

                    if (scrollY > 200) {
                        scrollToTopButton.classList.add('visible');
                        scrollToTopButton.style.display = 'flex';
                    } else {
                        scrollToTopButton.classList.remove('visible');
                        scrollToTopButton.style.display = 'none';
                    }
                });

                document.querySelectorAll(".social-video-icon.first").forEach(el => {
                    el.addEventListener("click", handleFirstIconClick);
                });

                document.querySelectorAll(".social-video-icon.second").forEach(el => {
                    el.addEventListener("click", handleSecondIconClick);
                });

                function handleFirstIconClick() {
                    const container = this.closest(".video-container");
                    const iframe = container.querySelector("iframe");

                    let ww = container.offsetWidth;
                    let hh = container.offsetHeight;

                    container.querySelector("iframe").classList.remove("hidden");
                    container.querySelector("img").classList.add("hidden");

                    if (!iframe) return;

                    const videoId = iframe.dataset.video;

                    const player = YT.get(iframe.id);
                    if(player) {
                        player.playVideo();
                        player.mute();
                        if(iframe.dataset.setvolume == 1) {
                            player.unMute();
                        }
                    } else {
                        new YT.Player(iframe, {
                            videoId: videoId,
                            events: {
                                'onReady': (event) => {
                                    event.target.playVideo();
                                    event.target.mute();
                                    if(iframe.dataset.setvolume == 1) {
                                        event.target.unMute();
                                    }
                                }
                            }
                        });
                    }

                    this.classList.add("hidden");
                    this.parentNode.querySelector(".social-video-icon.second").classList.remove("hidden");
                    container.querySelector(".social-video").classList.add("play-video-hover");
                    container.style.minWidth = ww + "px";

                    resizeYouTubePlayer(
                        [iframe],
                        parseFloat(iframe.dataset.ratio || ww / hh),
                        ".video-container"
                    );
                }

                function handleSecondIconClick() {
                    const container = this.closest(".video-container");
                    const iframe = container.querySelector("iframe");
                    container.querySelector("iframe").classList.add("hidden");
                    container.querySelector("img").classList.remove("hidden");

                    if (!iframe) return;

                    const videoId = iframe.dataset.video;

                    const player = YT.get(iframe.id);
                    if (player) {
                        player.pauseVideo();
                    } else {
                        new YT.Player(iframe, {
                            videoId: videoId,
                            events: {
                                'onReady': (event) => {
                                    event.target.pauseVideo();
                                }
                            }
                        });
                    }

                    this.classList.add("hidden");
                    this.parentNode.querySelector(".social-video-icon.first").classList.remove("hidden");
                    container.querySelector(".social-video").classList.remove("play-video-hover");
                    container.style.minWidth = "";
                }

                function resizeYouTubePlayer(iframes, ratio, videoType = ".video-bg") {
                    if (!iframes.length) return;

                    const win = document.querySelector(videoType);
                    const width = win ? win.offsetWidth : 0;
                    const height = win ? win.offsetHeight : 0;

                    iframes.forEach(iframe => {
                        if (iframe.id === 'vimeo-video') return;
                        let aspectRatio = parseFloat(iframe.dataset.ratio) || ratio || 16 / 9;

                        if (width / aspectRatio < height) {
                            const playerWidth = Math.ceil(height * aspectRatio);
                            Object.assign(iframe, {
                                width: playerWidth,
                                height: height
                            });
                            Object.assign(iframe.style, {
                                left: `${(width - playerWidth) / 2}px`,
                                top: `0`
                            });
                        } else {
                            const playerHeight = Math.ceil(width / aspectRatio);
                            Object.assign(iframe, {
                                width: width,
                                height: playerHeight
                            });
                            Object.assign(iframe.style, {
                                left: `0`,
                                top: `${(height - playerHeight) / 2}px`
                            });
                        }
                    });
                }
            });
        </script>
    </body>
</html>
