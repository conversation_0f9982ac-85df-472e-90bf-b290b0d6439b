{!! view_render_event('bagisto.shop.layout.footer.before') !!}

<!--
    The category repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->
@inject('themeCustomizationRepository', 'Webkul\Theme\Repositories\ThemeCustomizationRepository')

<!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->
@php
    $channel = core()->getCurrentChannel();

    $collectionLinks = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'name' => 'Collections',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);

    $servicesLinks = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'name' => 'Customer Service',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);

    $legalLinks = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'name' => 'Legal',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);

    $socialLinks = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'name' => 'Get Social',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);

    $paymentTypes = $themeCustomizationRepository->findOneWhere([
        'type'       => 'image_carousel',
        'name' => 'Payment Types',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);
@endphp

<footer class="tracking-wide bg-secondary px-5 md:px-9 pt-20 pb-12 relative" id="footer">
    <div class="flex flex-wrap justify-between gap-10">
        <div class="max-md:w-full md:max-w-md"
        data-aos="fade-up" data-aos-duration="400" data-aos-easing="ease" data-aos-delay="100" data-aos-anchor="#footer"
        >
            <a href="#" class="inline-flex mb-14 xl:mb-[76px]">
                <img src="{{ asset('widian-assets/images/logo-white.svg') }}" alt="logo" class="w-36 lg:w-48 xl:w-[290px]" />
            </a>

            @if ($socialLinks?->options)
                <h4 class="font-semibold text-white text-lg uppercase mb-6">
                    @lang('shop::app.components.layouts.footer.get-social')
                </h4>

                <ul class="space-y-4">
                    @foreach ($socialLinks->options['column_1'] as $socialLink)
                        <li class="text-white text-base font-semibold">
                            <a class="flex gap-3" href="{{ $socialLink['url'] }}"><span>{{ $socialLink['title'] }}</span><img src="{{ asset('widian-assets/images/arrow-up-right.svg') }}" alt="arrow-up-right" /></a>
                        </li>
                    @endforeach
                </ul>
            @endif
        </div>

        <div class="min-w-[140px]"
        data-aos="fade-up" data-aos-duration="400" data-aos-easing="ease" data-aos-delay="200" data-aos-anchor="#footer"
        >
            @if ($collectionLinks?->options)
                <h4 class="text-white font-semibold text-lg uppercase">
                    @lang('shop::app.components.layouts.footer.collections')
                </h4>
                <ul class="mt-6 mb-9 space-y-5 text-sm text-white">
                    @foreach ($collectionLinks->options['column_1'] as $collectionLink)
                        <li class="leading-snug text-base text-white">
                            <a href="{{ $collectionLink['url'] }}" class="hover:text-dark-gray">{{ $collectionLink['title'] }}</a>
                        </li>
                    @endforeach
                </ul>
            @endif

            @if ($paymentTypes?->options)
                <ul class="flex flex-wrap gap-2">
                    @foreach ($paymentTypes->options['images'] as $type)
                        <li class="bg-white w-11 h-7 relative rounded flex justify-center items-center">
                            <img src="{{ asset($type['image']) }}" alt="{{ $type['title'] }}" />
                        </li>
                    @endforeach
                </ul>
            @endif
        </div>

        <div class="min-w-[140px]"
        data-aos="fade-up" data-aos-duration="400" data-aos-easing="ease" data-aos-delay="300" data-aos-anchor="#footer"
        >
            @if ($servicesLinks?->options)
                <h4 class="text-white font-semibold text-lg uppercase">
                    @lang('shop::app.components.layouts.footer.customer-service')
                </h4>
                <ul class="mt-6 space-y-5 text-sm text-white">
                    @foreach ($servicesLinks->options['column_1'] as $service)
                        <li class="leading-snug text-base text-white">
                            <a href="{{ $service['url'] }}" class="hover:text-dark-gray">{{ $service['title'] }}</a>
                        </li>
                    @endforeach
                </ul>
            @endif
        </div>

        <div class="min-w-[140px]"
        data-aos="fade-up" data-aos-duration="400" data-aos-easing="ease" data-aos-delay="400" data-aos-anchor="#footer"
        >
            @if ($legalLinks?->options)
                <h4 class="text-white font-semibold text-lg uppercase">
                    @lang('shop::app.components.layouts.footer.legal')
                </h4>
                <ul class="mt-6 space-y-5 text-sm text-white">
                    @foreach ($legalLinks->options['column_1'] as $legal)
                        <li class="leading-snug text-base text-white">
                            <a href="{{ $legal['url'] }}" class="hover:text-dark-gray">{{ $legal['title'] }}</a>
                        </li>
                    @endforeach
                </ul>
            @endif
        </div>

        <!-- News Letter subscription -->
        <div class="w-full md:w-[380px]"
        data-aos="fade-up" data-aos-duration="400" data-aos-easing="ease" data-aos-delay="500" data-aos-anchor="#footer"
        >
            @if (core()->getConfigData('customer.settings.newsletter.subscription'))
                <h4
                    class="text-white font-semibold text-lg uppercase mb-6"
                    role="heading"
                    aria-level="2"
                >
                    @lang('shop::app.components.layouts.footer.newsletter-text')
                </h4>

                <p class="text-base text-white mb-8">
                    @lang('shop::app.components.layouts.footer.subscribe-stay-touch')
                </p>

                <div class="mb-8">
                    <x-shop::form
                        :action="route('shop.subscription.store', app()->getLocale())"
                        class="mt-2.5 rounded max-sm:mt-0"
                    >
                        <div class="relative w-full">
                            <x-shop::form.control-group.control
                                type="email"
                                class="block w-[420px] max-w-full h-12 px-6 py-2 !mb-5 rounded-xl border border-white focus:outline-none bg-transparent text-white placeholder:text-white text-base max-1060:w-full max-md:p-3.5 max-sm:mb-0 max-sm:rounded-lg max-sm:border-2 max-sm:p-2 max-sm:text-sm"
                                name="email"
                                rules="required|email"
                                label="E-mail"
                                :aria-label="trans('shop::app.components.layouts.footer.email')"
                                :placeholder="trans('shop::app.components.layouts.footer.email')"
                            />

                            <x-shop::form.control-group.error control-name="email" class="mb-2" />

                            <button
                                type="submit"
                                class="bg-white font-normal text-[#3d3c3a] px-6 py-2.5 rounded-xl"
                            >
                                @lang('shop::app.components.layouts.footer.subscribe')
                            </button>
                        </div>
                    </x-shop::form>
                </div>

                <p class="text-sm text-white leading-snug">
                    @lang('shop::app.components.layouts.footer.newsletter-disclaimer')
                </p>
            @endif
        </div>
    </div>
</footer>

@pushOnce('scripts')
    <script>
        // Ensure footer animations work properly
        document.addEventListener('DOMContentLoaded', function() {
            // Refresh AOS when footer comes into view
            const footer = document.getElementById('footer');
            if (footer && window.Aos) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Refresh AOS to ensure footer animations trigger
                            setTimeout(() => {
                                window.Aos.refresh();
                                if (window.refreshAOS) {
                                    window.refreshAOS();
                                }
                            }, 100);
                            observer.unobserve(footer);
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                observer.observe(footer);
            }
        });
    </script>
@endPushOnce

{!! view_render_event('bagisto.shop.layout.footer.after') !!}
