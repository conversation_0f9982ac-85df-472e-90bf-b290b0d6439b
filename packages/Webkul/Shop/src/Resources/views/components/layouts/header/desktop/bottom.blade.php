{!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.before') !!}

<div class="flex items-center flex-wrap lg:justify-center gap-4 pt-5 py-2 sm:px-10 px-4 min-h-[75px]">
    <!--
        This section will provide categories for the first, second, and third levels. If
        additional levels are required, users can customize them according to their needs.
    -->
    <!-- Left Nagivation Section -->
    <div class="left-10 absolute z-1 flex items-center gap-3 px-4 py-2.5 rounded max-lg:hidden">

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.locale_switcher.before') !!}

        <div class="items-center h-5 min-w-5 max-w-5">
            <img src="{{ asset('widian-assets/images/world.svg') }}" class="h-5 min-w-5 max-w-5" alt="@lang('shop::app.components.layouts.header.desktop.top.default-locale')">
        </div>

        <!-- Locales Switcher -->
        <x-shop::dropdown position="bottom-{{ core()->getCurrentLocale()->direction === 'ltr' ? 'right' : 'left' }}">
            <x-slot:toggle>
                <!-- Dropdown Toggler -->
                    <div class="flex cursor-pointer items-center gap-1">
                        <span class="uppercase hover:underline mt-1 text-xs">
                            {{ core()->getCurrentChannel()->locales()->orderBy('name')->where('code', app()->getLocale())->value('code') }}
                        </span>
                        <img
                            src="{{ ! empty(core()->getCurrentLocale()->logo_url)
                                    ? core()->getCurrentLocale()->logo_url
                                    : bagisto_asset('images/default-language.svg')
                                }}"
                            class="h-full"
                            alt="@lang('shop::app.components.layouts.header.desktop.top.default-locale')"
                            width="16"
                            height="16"
                        />

                        <span
                            class="text-xl"
                            :class="{'icon-arrow-up': localeToggler, 'icon-arrow-down': ! localeToggler}"
                            role="presentation"
                        ></span>
                    </div>
            </x-slot>

            <!-- Dropdown Content -->
            <x-slot:content class="!p-0">
                <v-locale-switcher></v-locale-switcher>
            </x-slot>
        </x-shop::dropdown>

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.locale_switcher.after') !!}

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.currency_switcher.before') !!}

        <!-- Currency Switcher -->
        <x-shop::dropdown>
            <x-slot:toggle>
                <div
                    class="flex cursor-pointer items-center gap-1"
                    role="button"
                    tabindex="0"
                    @click="currencyToggler = ! currencyToggler"
                >
                    <span class="uppercase hover:underline mt-1 text-xs">
                        {{ core()->getCurrentCurrencyCode() }}
                    </span>

                    <img
                        src="{{ ! empty(core()->getCurrentCurrency()->logo_url)
                                ? core()->getCurrentCurrency()->logo_url
                                : bagisto_asset('images/default-language.svg')
                            }}"
                        class="h-full"
                        alt="@lang('shop::app.components.layouts.header.desktop.top.default-currency')"
                        width="16"
                        height="16"
                    />

                    <span
                        class="text-xl"
                        :class="{'icon-arrow-up': currencyToggler, 'icon-arrow-down': ! currencyToggler}"
                        role="presentation"
                    ></span>
                </div>
            </x-slot>


            <x-slot:content class="!p-0">
                <v-currency-switcher></v-currency-switcher>
            </x-slot>
        </x-shop::dropdown>

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.currency_switcher.after') !!}

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.logo.before') !!}



        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.logo.after') !!}

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.category.before') !!}

        {{-- <v-desktop-category>
            <div class="flex items-center gap-5">
                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>
            </div>
        </v-desktop-category> --}}

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.category.after') !!}
    </div>
    <a
        href="{{ route('shop.home.index', app()->getLocale()) }}"
        aria-label="@lang('shop::app.components.layouts.header.bagisto')"
        class="py-6 widian-header-logo"
        data-aos="zoom-in-down" data-aos-duration="1000" data-aos-easing="ease-out"
    >
        <img
            src="{{ core()->getCurrentChannel()->logo_url ?? bagisto_asset('images/logo.svg') }}"
            class="w-36 lg:w-48 xl:w-[290px]"
            alt="{{ config('app.name') }}"
        >
    </a>
    <!-- Right Nagivation Section -->
    <div class="lg:absolute lg:right-10 flex items-center ml-auto gap-6">

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.search_bar.before') !!}

        <!-- Search Bar Container -->
        <div class="relative w-full">
            <button class="flex gap-2 items-center md:me-[137px] focus:outline-none" onclick="toggleSearchBar()">
                <img src="{{ asset('widian-assets/images/search.svg') }}" alt="Search">
                <span class="text-black text-xs font-normal leading-none hidden md:inline-flex">@lang('shop::app.components.layouts.header.search')</span>
            </button>
            {{-- <form
                action="{{ route('shop.search.index', app()->getLocale()) }}"
                class="flex max-w-[291px] items-center"
                role="search"
            >
                <label
                    for="organic-search"
                    class="sr-only"
                >
                    @lang('shop::app.components.layouts.header.search')
                </label>

                <img class="pointer-events-none absolute top-2.5 flex items-center text-xl ltr:left-3 rtl:right-3" src="{{ asset('widian-assets/images/search.svg') }}" alt="search">

                <input
                    type="text"
                    name="query"
                    value="{{ request('query') }}"
                    class="block w-full rounded-lg border border-transparent bg-transparent px-11 py-3 text-xs font-medium text-gray-900 transition-all hover:border-gray-400 focus:border-gray-400"
                    minlength="{{ core()->getConfigData('catalog.products.search.min_query_length') }}"
                    maxlength="{{ core()->getConfigData('catalog.products.search.max_query_length') }}"
                    placeholder="@lang('shop::app.components.layouts.header.search')"
                    aria-label="@lang('shop::app.components.layouts.header.search-text')"
                    aria-required="true"
                    pattern="[^\\]+"
                    required
                >

                <button
                    type="submit"
                    class="hidden"
                    aria-label="@lang('shop::app.components.layouts.header.submit')"
                >
                </button>
            </form> --}}
        </div>

        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.search_bar.after') !!}

        <!-- Right Navigation Links -->
        <div class="mt-1.5 flex gap-x-6">

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.compare.before') !!}

            <!-- Compare -->
            {{-- @if(core()->getConfigData('catalog.products.settings.compare_option'))
                <a
                    href="{{ route('shop.compare.index', app()->getLocale()) }}"
                    aria-label="@lang('shop::app.components.layouts.header.compare')"
                >
                    <span
                        class="icon-compare inline-block cursor-pointer text-2xl"
                        role="presentation"
                    ></span>
                </a>
            @endif --}}

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.compare.after') !!}

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.mini_cart.before') !!}

            <!-- Mini cart -->
            @if(core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                @include('shop::checkout.cart.mini-cart')
            @endif

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.mini_cart.after') !!}

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.profile.before') !!}

            <!-- user profile -->
            <x-shop::dropdown position="bottom-{{ core()->getCurrentLocale()->direction === 'ltr' ? 'right' : 'left' }}">
                <x-slot:toggle>
                    <span
                        class="inline-block cursor-pointer text-2xl"
                        role="button"
                        aria-label="@lang('shop::app.components.layouts.header.profile')"
                        tabindex="0"
                    >
                        <img src="{{ asset('widian-assets/images/user.svg') }}" alt="icon" class="h-5 min-w-5 max-w-5">
                    </span>
                </x-slot>

                <!-- Guest Dropdown -->
                @guest('customer')
                    <x-slot:content>
                        <div class="grid gap-2.5">
                            <p class="font-dmserif text-xl">
                                @lang('shop::app.components.layouts.header.welcome-guest')
                            </p>

                            <p class="text-sm">
                                @lang('shop::app.components.layouts.header.dropdown-text')
                            </p>
                        </div>

                        <p class="mt-3 w-full border border-zinc-200"></p>

                        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.customers_action.before') !!}

                        <div class="mt-6 flex gap-4">
                            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.sign_in_button.before') !!}

                            <a
                                href="{{ route('shop.customer.session.create', app()->getLocale()) }}"
                                class="bg-secondary text-white m-0 mx-auto block w-max rounded-lg px-7 place-content-center text-center text-base leading-none ltr:ml-0 rtl:mr-0"
                            >
                                @lang('shop::app.components.layouts.header.sign-in')
                            </a>

                            <a
                                href="{{ route('shop.customers.register.index', app()->getLocale()) }}"
                                class="border-secondary text-secondary m-0 mx-auto block w-max rounded-lg border-2 px-7 py-3 place-content-center text-center leading-none ltr:ml-0 rtl:mr-0"
                            >
                                @lang('shop::app.components.layouts.header.sign-up')
                            </a>

                            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.sign_up_button.after') !!}
                        </div>

                        {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.customers_action.after') !!}
                    </x-slot>
                @endguest

                <!-- Customers Dropdown -->
                @auth('customer')
                    <x-slot:content class="!p-0">
                        <div class="grid gap-2.5 p-5 pb-0">
                            <p class="font-dmserif text-xl">
                                @lang('shop::app.components.layouts.header.welcome')’
                                {{ auth()->guard('customer')->user()->first_name }}
                            </p>

                            <p class="text-sm">
                                @lang('shop::app.components.layouts.header.dropdown-text')
                            </p>
                        </div>

                        <p class="mt-3 w-full border border-zinc-200"></p>

                        <div class="mt-2.5 grid gap-1 pb-2.5">
                            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.profile_dropdown.links.before') !!}

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="{{ route('shop.customers.account.profile.index', app()->getLocale()) }}"
                            >
                                @lang('shop::app.components.layouts.header.profile')
                            </a>

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="{{ route('shop.customers.account.orders.index', app()->getLocale()) }}"
                            >
                                @lang('shop::app.components.layouts.header.orders')
                            </a>

                            @if (core()->getConfigData('customer.settings.wishlist.wishlist_option'))
                                <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="{{ route('shop.customers.account.wishlist.index', app()->getLocale()) }}"
                                >
                                    @lang('shop::app.components.layouts.header.wishlist')
                                </a>
                            @endif

                            <!--Customers logout-->
                            @auth('customer')
                                <x-shop::form
                                    method="DELETE"
                                    action="{{ route('shop.customer.session.destroy', app()->getLocale()) }}"
                                    id="customerLogout"
                                />

                                <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="{{ route('shop.customer.session.destroy', app()->getLocale()) }}"
                                    onclick="event.preventDefault(); document.getElementById('customerLogout').submit();"
                                >
                                    @lang('shop::app.components.layouts.header.logout')
                                </a>
                            @endauth

                            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.profile_dropdown.links.after') !!}
                        </div>
                    </x-slot>
                @endauth
            </x-shop::dropdown>

            {!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.profile.after') !!}
        </div>
    </div>
</div>

{!! view_render_event('bagisto.shop.components.layouts.header.desktop.bottom.after') !!}
